{"name": "OpenEdenVault", "scripts": {"clean": "rm -rf cache artifacts typechain typechain-types", "compile": "SKIP_LOAD=true npx hardhat compile", "size": "npx hardhat size-contracts", "deploy": "npx hardhat run --network goerli scripts/deploy.js", "coverage": "npx hardhat coverage", "deploy_eth_mainnet": "npx hardhat run --network eth_main scripts/deploy.js", "test": "npx hardhat test", "generate": "npx mnemonics", "networks": "npx hardhat verify --list-networks", "format": "npm run format-ts && npm run format-sol", "format-sol": "prettier --write 'contracts/**/*.sol'", "format-ts": "prettier --ignore-unknown --write \"**/*.ts\""}, "devDependencies": {"@chainlink/contracts": "^0.6.0", "@nomicfoundation/hardhat-network-helpers": "^1.0.8", "@nomicfoundation/hardhat-toolbox": "^2.0.1", "@openzeppelin/contracts": "^4.9.0", "@openzeppelin/contracts-upgradeable": "^4.8.2", "@openzeppelin/hardhat-upgrades": "^1.22.1", "@rari-capital/solmate": "^6.4.0", "colors.ts": "^1.0.20", "hardhat": "^2.12.4", "hardhat-contract-sizer": "^2.8.0", "hardhat-storage-layout": "^0.1.7", "prettier": "^2.8.4", "prettier-plugin-solidity": "^1.1.3", "solidity-coverage": "^0.8.5"}, "dependencies": {"@types/colors": "^1.2.1", "colors": "^1.4.0", "config": "^3.3.9", "dotenv": "^16.0.3", "fs-extra": "^11.1.1", "lowdb": "^1.0.0", "reconnecting-websocket": "^4.4.0", "tmp-promise": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^4.9.3", "web3": "^1.9.0", "web3-utils": "^1.8.2", "websocket": "^1.0.34", "ws": "^8.12.0"}}