import { task } from "hardhat/config";
import { deployMockBuidlRedemption } from "../../helpers/contracts-deployments";
import { getMockBuidl } from "../../helpers/contracts-getters";

// export NODE_ENV=staging
// npx hardhat full:deploy_buidl_redemption  --network sepolia --verify

task("full:deploy_buidl_redemption", "Deploy mock buidl redemption")
  .addFlag("verify", "Verify contracts at Etherscan")
  .setAction(async ({ verify }, localDRE) => {
    console.log("verify:", verify);
    localDRE.run("set-DRE");

    const buidl = await getMockBuidl();
    // const mockUSDC = "******************************************"; // sepolia
    const mockUSDC = "******************************************"; // arbi sepolia

    const buidlRedemption = await deployMockBuidlRedemption(
      buidl.address,
      mockUSDC,
      verify
    );
    console.log("buidlRedemption address:", buidlRedemption.address);
  });
